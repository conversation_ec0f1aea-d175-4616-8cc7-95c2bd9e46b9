"use client"

import { Employee, <PERSON>p<PERSON><PERSON><PERSON><PERSON> } from "@/lib/types"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { 
  Briefcase, 
  Mail, 
  MapPin, 
  Building, 
  DollarSign,
  Linkedin,
  Twitter,
  BarChart3,
  Edit,
  Eye,
  EyeOff
} from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { getCurrentUser } from "@/lib/auth"

interface EmployeeProfileProps {
  employee: Employee & {
    kpis?: EmployeeKPI[]
  }
  canEdit?: boolean
  currentUserRole?: string
  currentUserId?: string
}

export function EmployeeProfile({ 
  employee, 
  canEdit = false,
  currentUserRole,
  currentUserId
}: EmployeeProfileProps) {
  const [showRate, setShowRate] = useState(false)
  
  // Check if current user can view rates
  const canViewRates = 
    currentUserRole === 'accountant' || 
    currentUserRole === 'hr-admin' || 
    currentUserRole === 'super-admin' ||
    currentUserId === 'user_2qHKqdkczZYNGYMjPLLcKtlBP68' // Bob's ID

  return (
    <div className="space-y-6">
      {/* Header Card */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <CardTitle className="text-2xl">
                {employee.firstName} {employee.lastName}
              </CardTitle>
              <CardDescription>{employee.fullName}</CardDescription>
            </div>
            {canEdit && (
              <Link href={`/dashboard/employees/${employee.id}/profile/edit`}>
                <Button variant="outline" size="sm">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Profile
                </Button>
              </Link>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Contact Information */}
          <div className="grid gap-4 md:grid-cols-2">
            {employee.email && (
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <a href={`mailto:${employee.email}`} className="text-sm hover:underline">
                  {employee.email}
                </a>
              </div>
            )}
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{employee.departmentName || 'No Department'}</span>
            </div>
          </div>

          {/* Social Links */}
          <div className="flex gap-4">
            {employee.linkedinUrl && (
              <a 
                href={employee.linkedinUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-sm text-blue-600 hover:underline"
              >
                <Linkedin className="h-4 w-4" />
                LinkedIn
              </a>
            )}
            {employee.twitterUrl && (
              <a 
                href={employee.twitterUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-sm text-sky-500 hover:underline"
              >
                <Twitter className="h-4 w-4" />
                X (Twitter)
              </a>
            )}
          </div>

          <Separator />

          {/* Compensation (with access control) */}
          {canViewRates && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Compensation</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowRate(!showRate)}
                  className="h-auto p-1"
                >
                  {showRate ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                </Button>
              </div>
              {showRate && (
                <div className="pl-6">
                  <Badge variant="secondary">
                    ${employee.rate.toLocaleString()} / {employee.compensation}
                  </Badge>
                </div>
              )}
            </div>
          )}

          {/* Bio */}
          {employee.bio && (
            <>
              <Separator />
              <div className="space-y-2">
                <h3 className="text-sm font-medium">About</h3>
                <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                  {employee.bio}
                </p>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* KPIs Card */}
      {employee.kpis && employee.kpis.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Key Performance Indicators
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {employee.kpis.map((kpi) => (
                <div key={kpi.id} className="space-y-2">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <h4 className="text-sm font-medium">{kpi.kpiName}</h4>
                      {kpi.description && (
                        <p className="text-xs text-muted-foreground">{kpi.description}</p>
                      )}
                    </div>
                    {kpi.period && (
                      <Badge variant="outline" className="text-xs">
                        {kpi.period}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-4 text-sm">
                    {kpi.kpiValue && (
                      <div>
                        <span className="text-muted-foreground">Current: </span>
                        <span className="font-medium">
                          {kpi.kpiValue}
                          {kpi.kpiUnit && ` ${kpi.kpiUnit}`}
                        </span>
                      </div>
                    )}
                    {kpi.kpiTarget && (
                      <div>
                        <span className="text-muted-foreground">Target: </span>
                        <span className="font-medium">
                          {kpi.kpiTarget}
                          {kpi.kpiUnit && ` ${kpi.kpiUnit}`}
                        </span>
                      </div>
                    )}
                  </div>
                  {(kpi.kpiValue && kpi.kpiTarget && !isNaN(Number(kpi.kpiValue)) && !isNaN(Number(kpi.kpiTarget))) && (
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full transition-all"
                        style={{ 
                          width: `${Math.min(100, (Number(kpi.kpiValue) / Number(kpi.kpiTarget)) * 100)}%` 
                        }}
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Employment Status */}
      <Card>
        <CardHeader>
          <CardTitle>Employment Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Status</span>
              <Badge variant={employee.active ? "default" : "secondary"}>
                {employee.active ? "Active" : "Inactive"}
              </Badge>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Employment Type</span>
              <span className="font-medium capitalize">{employee.compensation}</span>
            </div>
            {employee.managers && employee.managers.length > 0 && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Reports To</span>
                <div className="flex flex-col items-end gap-1">
                  {employee.managers.map((manager) => (
                    <span key={manager.id} className="font-medium">
                      {manager.managerName}
                      {manager.isPrimary && (
                        <Badge variant="outline" className="ml-2 text-xs">Primary</Badge>
                      )}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}